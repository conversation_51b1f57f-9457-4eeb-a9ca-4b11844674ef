"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Store, MapPin, Phone, Mail, Clock, Star, Info } from "lucide-react"

export interface BusinessInfo {
  name: string
  description?: string
  phone?: string
  email?: string
  address?: string
  postcode?: string
  hygiene_rating?: string
  allergens_info?: string
  business_type?: string
}

interface BusinessInfoFormProps {
  businessId: number | null
  authToken: string
}

export function BusinessInfoForm({
  businessId,
  authToken
}: BusinessInfoFormProps) {
  const [data, setData] = useState<BusinessInfo>({
    name: '',
    description: '',
    phone: '',
    email: '',
    address: '',
    postcode: '',
    hygiene_rating: '',
    allergens_info: '',
    business_type: ''
  })
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [businessTypes, setBusinessTypes] = useState<Array<{ id: string; name: string }>>([])

  // Fetch business data when businessId changes
  useEffect(() => {
    if (businessId && authToken) {
      fetchBusinessData()
    }
  }, [businessId, authToken])

  const fetchBusinessData = async () => {
    try {
      setLoading(true)

      const response = await fetch(`/api/admin/businesses-direct`, {
        headers: {
          'Authorization': authToken ? `Bearer ${authToken}` : '',
          'Cache-Control': 'no-cache',
        },
      })

      if (response.ok) {
        const result = await response.json()
        const businesses = result.businesses || []
        const business = businesses.find((b: any) => b.id === businessId)

        if (business) {
          setData({
            name: business.name || '',
            description: business.description || '',
            phone: business.phone || '',
            email: business.email || '',
            address: business.address || '',
            postcode: business.postcode || '',
            hygiene_rating: business.hygiene_rating || '',
            allergens_info: business.allergens_info || '',
            business_type: business.business_type_id?.toString() || ''
          })
        }
      }
    } catch (error) {
      console.error('Error fetching business data:', error)
    } finally {
      setLoading(false)
    }
  }

  const onChange = (field: keyof BusinessInfo, value: string) => {
    setData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const onSave = async () => {
    if (!businessId || !authToken) return

    try {
      setSaving(true)
      // TODO: Implement save functionality
      console.log('Saving business data:', data)
    } catch (error) {
      console.error('Error saving business data:', error)
    } finally {
      setSaving(false)
    }
  }

  if (!businessId) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Store className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Business Selected</h3>
          <p className="text-gray-600">Select a business from the header to manage its settings.</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded w-1/3"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="h-10 bg-gray-200 rounded"></div>
                <div className="h-20 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card className="border border-gray-200">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="bg-gray-800 rounded-lg p-2">
              <Store className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Basic Information</CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                Essential business details that customers will see
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                Business Name *
              </Label>
              <Input
                id="name"
                value={data.name || ""}
                onChange={(e) => onChange('name', e.target.value)}
                placeholder="Enter your business name"
                className="focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="business_type" className="text-sm font-medium text-gray-700">
                Business Type
              </Label>
              <Select
                value={data.business_type || ""}
                onValueChange={(value) => onChange('business_type', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select business type" />
                </SelectTrigger>
                <SelectContent>
                  {businessTypes.map((type) => (
                    <SelectItem key={type.id} value={type.id}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="description" className="text-sm font-medium text-gray-700">
                Description
              </Label>
              <Textarea
                id="description"
                value={data.description || ""}
                onChange={(e) => onChange('description', e.target.value)}
                placeholder="Describe your business..."
                rows={3}
                className="focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card className="border border-gray-200">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="bg-gray-800 rounded-lg p-2">
              <Phone className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Contact Information</CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                How customers can reach you
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
                Phone Number
              </Label>
              <Input
                id="phone"
                value={data.phone || ""}
                onChange={(e) => onChange('phone', e.target.value)}
                placeholder="e.g. 01534 123456"
                className="focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                value={data.email || ""}
                onChange={(e) => onChange('email', e.target.value)}
                placeholder="<EMAIL>"
                className="focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Location Information */}
      <Card className="border border-gray-200">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="bg-gray-800 rounded-lg p-2">
              <MapPin className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Location</CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                Where customers can find you
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="address" className="text-sm font-medium text-gray-700">
                Address
              </Label>
              <Input
                id="address"
                value={data.address || ""}
                onChange={(e) => onChange('address', e.target.value)}
                placeholder="Street address"
                className="focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="postcode" className="text-sm font-medium text-gray-700">
                Postcode
              </Label>
              <Input
                id="postcode"
                value={data.postcode || ""}
                onChange={(e) => onChange('postcode', e.target.value)}
                placeholder="JE1 1AA"
                className="focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Additional Information */}
      <Card className="border border-gray-200">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="bg-gray-800 rounded-lg p-2">
              <Info className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Additional Information</CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                Optional details for customers
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="hygiene_rating" className="text-sm font-medium text-gray-700">
                Hygiene Rating
                <Badge variant="secondary" className="ml-2 text-xs">Optional</Badge>
              </Label>
              <Select
                value={data.hygiene_rating || ""}
                onValueChange={(value) => onChange('hygiene_rating', value)}
              >
                <SelectTrigger className="max-w-md">
                  <SelectValue placeholder="Select hygiene rating" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">⭐⭐⭐⭐⭐ 5 - Very Good</SelectItem>
                  <SelectItem value="4">⭐⭐⭐⭐ 4 - Good</SelectItem>
                  <SelectItem value="3">⭐⭐⭐ 3 - Generally Satisfactory</SelectItem>
                  <SelectItem value="2">⭐⭐ 2 - Improvement Necessary</SelectItem>
                  <SelectItem value="1">⭐ 1 - Major Improvement Necessary</SelectItem>
                  <SelectItem value="0">0 - Urgent Improvement Necessary</SelectItem>
                  <SelectItem value="exempt">Exempt</SelectItem>
                  <SelectItem value="awaiting">Awaiting Inspection</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="allergens_info" className="text-sm font-medium text-gray-700">
                Special Information
                <Badge variant="secondary" className="ml-2 text-xs">Optional</Badge>
              </Label>
              <Textarea
                id="allergens_info"
                value={data.allergens_info || ""}
                onChange={(e) => onChange('allergens_info', e.target.value)}
                placeholder="e.g. Allergen information, accessibility notes, or other important customer information..."
                rows={4}
                className="focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      {onSave && (
        <div className="flex justify-end">
          <Button onClick={onSave} disabled={saving} className="min-w-32">
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </Button>
        </div>
      )}
    </div>
  )
}
